import path from 'path';
import winston from 'winston';
const logDir = path.resolve(__dirname, '../../logs');
const logger = winston.createLogger({
  level: 'info', // can be changed to 'debug', 'warn', etc.
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json() // logs in structured JSON format
  ),
  transports: [
    new winston.transports.File({
      filename: path.join(logDir, 'requests.log'),
      handleExceptions: true,
      maxsize: 5242880, // 5MB
      maxFiles: 5
    }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ],
  exitOnError: false,
});
export default logger;
