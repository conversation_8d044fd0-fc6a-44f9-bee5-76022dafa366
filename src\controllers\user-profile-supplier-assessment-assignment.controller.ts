import {authenticate} from '@loopback/authentication';
import {
  UserCredentialsRepository,
  UserRepository
} from '@loopback/authentication-jwt';
import {inject} from '@loopback/core';
import {
  Count,
  CountSchema,
  Filter,
  repository,
  Where,
} from '@loopback/repository';
import {
  del,
  get,
  getModelSchemaRef,
  getWhereSchemaFor,
  HttpErrors,
  param,
  patch,
  post,
  requestBody,
} from '@loopback/rest';
import {UserProfile as LibUserProfile, SecurityBindings, securityId} from '@loopback/security';
import {DateTime} from 'luxon';
import {v4 as uuidv4} from 'uuid';
import {
  SupplierAssessmentAssignment,
  UserProfile,
} from '../models';
import {UserProfileRepository, VendorCodeRepository} from '../repositories';
import {SqsService} from '../services/sqs-service.service';
import {UserProfileController} from './user-profile.controller';
@authenticate('cognito-tvs', 'jwt')
export class UserProfileSupplierAssessmentAssignmentController {
  constructor(
    @repository(UserProfileRepository) protected userProfileRepository: UserProfileRepository,
    @repository(UserCredentialsRepository) protected userCredentialsRepository: UserCredentialsRepository,
    @repository(UserRepository) protected userRepository: UserRepository,
    @inject('controllers.UserProfileController') public userProfileController: UserProfileController,
    @repository(VendorCodeRepository) protected vendorCodeRepository: VendorCodeRepository,
    @inject('services.SqsService')
    public sqsService: SqsService,
  ) { }

  @get('/user-profiles/{id}/supplier-assessment-assignments', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many SupplierAssessmentAssignment',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SupplierAssessmentAssignment)},
          },
        },
      },
    },
  })
  async find(
    @inject(SecurityBindings.USER)
    currentUserProfile: LibUserProfile,
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SupplierAssessmentAssignment>,
  ): Promise<SupplierAssessmentAssignment[]> {

    const userId = currentUserProfile[securityId];
    const userDetail = await this.userRepository.findById(userId);
    const userProfileDetail = await this.userProfileRepository.findOne({
      where: {userId: userId}, include: [
        {
          relation: 'vendorCodes'
        },
      ], limit: 1
    });


    const supplierId = (filter?.where as {supplierId?: number})?.supplierId;
    const vendorCode = (filter?.where as {vendorCode?: number})?.vendorCode;
    if (!userProfileDetail || userProfileDetail.id !== supplierId) {
      throw new HttpErrors.Forbidden('You are not authorized to access this resource');
    }

    const match = userProfileDetail?.vendorCodes?.some(vc =>
      vc.code === vendorCode && vc.clientId === id
    );

    if (!match) {
      throw new HttpErrors.Forbidden('You are not authorized to access this resource');
    }
    return this.userProfileRepository.supplierAssessmentAssignments(id).find(filter);
  }

  @get('/user-profiles/{id}/supplier-assessment-assignments-global', {
    responses: {
      '200': {
        description: 'Array of UserProfile has many SupplierAssessmentAssignment',
        content: {
          'application/json': {
            schema: {type: 'array', items: getModelSchemaRef(SupplierAssessmentAssignment)},
          },
        },
      },
    },
  })
  async findGlobal(
    @param.path.number('id') id: number,
    @param.query.object('filter') filter?: Filter<SupplierAssessmentAssignment>,
  ): Promise<SupplierAssessmentAssignment[]> {

    return this.userProfileRepository.supplierAssessmentAssignments(id).find(filter);
  }
  // @post('/user-profiles/{id}/supplier-assessment-assignmentss', {
  //   responses: {
  //     '200': {
  //       description: 'UserProfile model instance',
  //       content: {'application/json': {schema: getModelSchemaRef(SupplierAssessmentAssignment)}},
  //     },
  //   },
  // })
  // async create(
  //   @param.path.number('id') id: typeof UserProfile.prototype.id,
  //   @requestBody({
  //     content: {
  //       'application/json': {
  //         schema: getModelSchemaRef(SupplierAssessmentAssignment, {
  //           title: 'NewSupplierAssessmentAssignmentInUserProfile',
  //           exclude: ['id'],
  //           optional: ['userProfileId']
  //         }),
  //       },
  //     },
  //   }) supplierAssessmentAssignment: Omit<SupplierAssessmentAssignment, 'id'>,
  // ): Promise<SupplierAssessmentAssignment> {
  //   return this.userProfileRepository.supplierAssessmentAssignments(id).create({id: uuidv4(), ...supplierAssessmentAssignment});
  // }
  @post('/user-profiles/{id}/supplier-assessment-assignments-global', {
    responses: {
      '200': {
        description: 'UserProfile model instance',
        content: {'application/json': {schema: getModelSchemaRef(SupplierAssessmentAssignment)}},
      },
    },
  })
  async createCustom(
    @param.path.number('id') id: typeof UserProfile.prototype.id,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAssessmentAssignment, {
            title: 'NewSupplierAssessmentAssignmentInUserProfile',
            exclude: ['id'],
            optional: ['userProfileId']
          }),
        },
      },
    }) supplierAssessmentAssignment: Omit<SupplierAssessmentAssignment, 'id'>,
  ): Promise<SupplierAssessmentAssignment> {
    let createdAssignment;
    const maxAttempts = 10;

    for (let attempt = 0; attempt < maxAttempts; attempt++) {
      const uuid = uuidv4()
      try {
        createdAssignment = await this.userProfileRepository.supplierAssessmentAssignments(id).create({
          id: uuid,
          ...supplierAssessmentAssignment
        });

        break; // success, break the loop
      } catch (err) {
        // handle only duplicate key error (adjust code for your DB if needed)
        const isDuplicateError =
          err.code === 'ER_DUP_ENTRY' ||       // MySQL
          err.code === 'SQLITE_CONSTRAINT' ||  // SQLite
          err.statusCode === 409;              // LoopBack's default for conflict

        if (!isDuplicateError) throw err; // rethrow other errors

        // else log and continue
        console.warn(`Duplicate ID on attempt ${attempt + 1}, retrying...`);
      }
    }

    // final failure after all attempts
    if (!createdAssignment) {
      throw new HttpErrors.InternalServerError('Could not generate unique assignment ID after multiple attempts.');
    } else {
      console.log('createdAssignment', createdAssignment);
    }



    const assessmentEndDate = DateTime.fromISO(supplierAssessmentAssignment.assessmentEndDate ?? '', {zone: 'utc'}).plus({'day': 1});
    // 2. Fetch vendor data based on vendorId in assignment
    const vendorData = await this.vendorCodeRepository.findById(supplierAssessmentAssignment.vendorId);
    const vendorSpoc = await this.userProfileController.filteredUP({where: {id: vendorData.userProfileId}});
    // const vendorSpoc = vendorSpocArray?.[0];
    const supplierOtherSpoc = this.getUniqueValidEmails([vendorData]).flatMap((x: any) => x.emails).filter((x: any) => x)
    const adminObj = await this.userProfileRepository.findById(id);

    console.log('vendorSpocmail', vendorSpoc)
    // 3. Trigger mail
    if (vendorData && vendorSpoc && vendorSpoc[0]?.email) {
      const subject = `Submit MSI Self-Assessment – ${vendorData.supplierName}`;
      const body = ` <p> Dear${vendorData.supplierName} </p>
  <p style="margin: 5px 0px;" >Hope you're doing well.</p>

  <p style="margin: 5px 0px;" >
    As part of our <strong>"My Sustainability Index"</strong> program, which aims to enhance sustainable practices across our value chain, we kindly request you to complete the <strong>"Self-Assessment"</strong> form.
  </p>

  <p style="margin: 5px 0px;" >
    Your Self-Assessment is now live on the Navigos Sustainability Platform. We request you to complete the assessment by the <strong>${assessmentEndDate.toFormat('dd-MM-yyyy')}</strong>.
  </p>

  <p><strong>What You Need to Do:</strong></p>
  <ul>
    <li><strong>Log in</strong> to the Navigos application <em>Supplier Portal: <a href=${adminObj?.supplierPortalUrl}>${adminObj?.supplierPortalUrl}</a></em> using your provided credentials</li>
    <li>Once logged in, navigate to the Self-Assessment section in the "My Actions" tab and submit your responses before the due date</li>
    <li><strong>Complete</strong> all sections of the form with accurate and updated information</li>
  </ul>

  <p style="margin: 5px 0px;" >
    The self-assessment form includes questions on your sustainability policies, initiatives, etc. Please upload supporting evidence (e.g., certifications, licenses, reports) within the form as appropriate to the question.
  </p>

  <p style="margin: 5px 0px;" >
    If you need support or clarification, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>, and kindly copy <a href="mailto:<EMAIL>"><EMAIL></a>.
  </p>

  <p style="margin: 5px 0px;" >
    Your participation is vital to the success of the <strong>"My Sustainability Index"</strong> program, and we greatly appreciate your cooperation in helping us build a sustainable future together.
  </p>

  <p style="margin: 5px 0px;" >Thank you once again for your commitment to this shared journey.<br/>
  Warm regards,<br/>
  <strong>TVS Motor Company Limited</strong></p>

  <p style='font-style:italic'><em>This is an automated message. Please do not respond to this mail</em></p>
`;

      try {
        this.sqsService.sendEmail([vendorSpoc[0]?.email, ...supplierOtherSpoc], subject, body, ['<EMAIL>', '<EMAIL>'])

      } catch (error) {
        console.error('Error sending email:', error);
        throw new Error('Failed to send email');
      }
    }

    return createdAssignment;
  }
  @patch('/user-profiles/{id}/supplier-assessment-assignments', {
    responses: {
      '200': {
        description: 'UserProfile.SupplierAssessmentAssignment PATCH success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async patch(
    @param.path.number('id') id: number,
    @requestBody({
      content: {
        'application/json': {
          schema: getModelSchemaRef(SupplierAssessmentAssignment, {partial: true}),
        },
      },
    })
    supplierAssessmentAssignment: Partial<SupplierAssessmentAssignment>,
    @param.query.object('where', getWhereSchemaFor(SupplierAssessmentAssignment)) where?: Where<SupplierAssessmentAssignment>,
  ): Promise<Count> {
    return this.userProfileRepository.supplierAssessmentAssignments(id).patch(supplierAssessmentAssignment, where);
  }

  @del('/user-profiles/{id}/supplier-assessment-assignments', {
    responses: {
      '200': {
        description: 'UserProfile.SupplierAssessmentAssignment DELETE success count',
        content: {'application/json': {schema: CountSchema}},
      },
    },
  })
  async delete(
    @param.path.number('id') id: number,
    @param.query.object('where', getWhereSchemaFor(SupplierAssessmentAssignment)) where?: Where<SupplierAssessmentAssignment>,
  ): Promise<Count> {
    return this.userProfileRepository.supplierAssessmentAssignments(id).delete(where);
  }
  getUniqueValidEmails(data: any) {
    const seenEmails = new Set(); // Global tracker for unique emails

    return data.map(({code, supplierEmail3, supplierEmail2}: any) => {
      const uniqueEmails = new Set(); // Local tracker to prevent duplicate emails within the same code

      [supplierEmail3, supplierEmail2].forEach((email: any) => {
        if (this.isValidEmail(email) && !seenEmails.has(email)) {
          uniqueEmails.add(email);
          seenEmails.add(email); // Track globally
        }
      });

      return {code, emails: [...uniqueEmails]};
    }).filter((entry: any) => entry.emails.length > 0); // Remove empty email lists
  }

  isValidEmail(email: any) {
    if (!email?.trim()) return false; // Returns false for null, undefined, or empty string
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
  };
}
