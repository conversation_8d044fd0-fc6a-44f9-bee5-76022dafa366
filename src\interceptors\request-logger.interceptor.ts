import {
  globalInterceptor,
  inject,
  Interceptor,
  InvocationContext,
  Provider
} from '@loopback/core';

import {RequestContext, RestBindings} from '@loopback/rest';
import logger from '../utils/logger';
@globalInterceptor('logging', {tags: {name: 'RequestLogger'}})
export class RequestLoggerInterceptor implements Provider<Interceptor> {
  constructor(@inject(RestBindings.Http.REQUEST) private ctx: RequestContext) { }
  value(): Interceptor {
    return async (invocationCtx: InvocationContext, next) => {
      const req = this.ctx.request;
      const {method, originalUrl, headers, body} = req;
      const start = Date.now();
      let responseData: any;
      let statusCode = 200;
      try {
        responseData = await next();
      } catch (err) {
        statusCode = err.statusCode || 500;
        responseData = {error: err.message};
        throw err;
      } finally {
        const duration = Date.now() - start;
        logger.info({
          timestamp: new Date().toISOString(),
          method,
          url: originalUrl,
          headers,
          body,
          statusCode,
          response: responseData,
          duration: `${duration}ms`,
        });
      }
      return responseData;
    };
  }
}
